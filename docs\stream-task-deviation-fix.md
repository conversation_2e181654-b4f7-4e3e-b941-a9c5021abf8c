# 流式页面任务偏差修复方案

## 问题根因

通过对比分析，发现流式页面任务偏差的根本原因是：

1. **任务规划误触发**: 流式页面在每次AI回复完成后都会执行任务规划判断，包括任务执行期间
2. **上下文管理复杂**: 流式页面的消息过滤逻辑可能导致重要上下文丢失
3. **状态管理不一致**: 流式页面和非流式页面的任务执行逻辑存在差异

## 立即修复方案

### 修复1: 禁用任务执行期间的任务规划判断

**文件**: `app/content-generator/content-generator-stream.tsx`
**位置**: `onFinish` 回调函数 (约第350行)

**修复前**:
```tsx
onFinish: (finalContent) => {
  console.log('流处理完成');

  // 添加AI回复到对话
  const messageId = `msg-${Date.now()}`;
  setConversation(prev => ({
    ...prev,
    messages: [
      ...prev.messages,
      {
        id: messageId,
        role: 'assistant' as const,
        content: finalContent,
        timestamp: Date.now(),
        type: 'assistant' as 'user' | 'assistant' | 'task' | 'system'
      }
    ]
  }));

  // 智能判断是否需要进行任务规划
  const shouldExtractTasks = shouldPerformTaskPlanning(userMessage.content, finalContent);
  
  if (shouldExtractTasks) {
    // 处理任务提取
    const extractedTasks = extractTasksFromResponse(finalContent);
    // ...
  }
}
```

**修复后**:
```tsx
onFinish: (finalContent) => {
  console.log('流处理完成');

  // 添加AI回复到对话
  const messageId = `msg-${Date.now()}`;
  setConversation(prev => ({
    ...prev,
    messages: [
      ...prev.messages,
      {
        id: messageId,
        role: 'assistant' as const,
        content: finalContent,
        timestamp: Date.now(),
        type: 'assistant' as 'user' | 'assistant' | 'task' | 'system'
      }
    ]
  }));

  // 检查是否在任务执行期间 - 新增检查
  if (executionPhase === 'executing' || tasks.length > 0) {
    console.log('任务执行期间，跳过任务规划判断，直接处理文件提取');
    // 直接处理文件提取，不进行任务规划
    const extractedFiles = extractMultipleFilesFromMessage(finalContent, messageId);
    if (extractedFiles.length > 0) {
      processExtractedFiles(extractedFiles);
    }
    return; // 重要：直接返回，不执行后续的任务规划逻辑
  }

  // 只在非任务执行期间进行任务规划判断
  console.log('非任务执行期间，进行任务规划判断');
  const shouldExtractTasks = shouldPerformTaskPlanning(userMessage.content, finalContent);
  
  if (shouldExtractTasks) {
    // 处理任务提取
    const extractedTasks = extractTasksFromResponse(finalContent);
    // ... 其余逻辑保持不变
  } else {
    // 不进行任务规划，直接处理文件提取
    const extractedFiles = extractMultipleFilesFromMessage(finalContent, messageId);
    if (extractedFiles.length > 0) {
      processExtractedFiles(extractedFiles);
    }
  }
}
```

### 修复2: 增强任务规划判断逻辑

在 `shouldPerformTaskPlanning` 函数中添加更严格的检查：

```tsx
const shouldPerformTaskPlanning = (userInput: string, aiResponse: string): boolean => {
  // 如果已经有任务在执行中，绝对不进行新的任务规划
  if (tasks.length > 0 && (executionPhase === 'executing' || executionPhase === 'planning')) {
    console.log('任务执行中或规划中，跳过任务规划');
    return false;
  }

  // 检查是否是任务执行的回复（包含任务执行相关的标识）
  const isTaskExecutionReply = /任务\d+.*?完成|执行.*?任务|生成.*?文件/.test(aiResponse);
  if (isTaskExecutionReply) {
    console.log('检测到任务执行回复，跳过任务规划');
    return false;
  }

  // 其余原有逻辑保持不变
  // ...
};
```

### 修复3: 简化上下文传递

参考非流式页面的简单上下文传递方式：

```tsx
// 在 executeNextTask 函数中简化消息准备逻辑
const response = await fetch('/api/chat-stream', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    messages: [
      // 简化：直接传递所有对话消息，不进行复杂过滤
      ...conversation.messages.map(msg => ({
        role: msg.role === 'task' ? 'user' : msg.role, // 只做必要的角色映射
        content: msg.content,
      })),
      {
        role: taskMessage.role === 'task' ? 'user' : taskMessage.role,
        content: taskMessage.content,
      }
    ],
    model,
  }),
});
```

## 验证方案

### 测试场景1: 正常任务规划
1. 输入: "写个关于AI的PPT"
2. 预期: 正常进行任务规划和执行
3. 验证: 检查控制台日志，确认任务规划判断正确

### 测试场景2: 任务执行期间的用户输入
1. 在任务执行期间输入: "修改一下颜色"
2. 预期: 跳过任务规划，直接处理文件修改
3. 验证: 检查控制台日志，确认显示"任务执行期间，跳过任务规划判断"

### 测试场景3: 任务完成后的反馈
1. 所有任务完成后输入: "第一个文件不对"
2. 预期: 不触发新的任务规划
3. 验证: 检查控制台日志，确认任务规划判断逻辑正确

## 实施步骤

1. **备份当前代码**: 确保可以回滚
2. **实施修复1**: 添加任务执行期间的检查逻辑
3. **测试验证**: 使用上述测试场景验证修复效果
4. **实施修复2和3**: 如果修复1效果良好，继续实施其他修复
5. **全面测试**: 确保所有功能正常工作

## 监控指标

修复后需要监控以下指标：
- 任务规划误触发次数（应该为0）
- 任务执行成功率
- 用户反馈处理正确率
- 控制台错误日志数量

## 长期优化建议

1. **统一架构**: 考虑将非流式页面的稳定逻辑完全移植到流式页面
2. **状态机模式**: 使用状态机来管理任务执行状态，避免状态混乱
3. **单元测试**: 为任务规划和执行逻辑添加单元测试
4. **代码重构**: 提取公共逻辑，减少代码重复
