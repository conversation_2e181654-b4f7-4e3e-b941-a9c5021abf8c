/**
 * 任务规划修复验证脚本
 * 
 * 验证首次对话能否正常提取任务，同时确保死循环问题已修复
 */

// 测试用例：模拟不同类型的AI回复
const testCases = [
  {
    name: '正常任务规划回复',
    userInput: '写个大语言模型的PPT',
    aiResponse: `感谢您的请求！基于您提供的设计指导和技术规范，我将为您规划一个关于大语言模型的PPT演示文稿。

【任务1】封面页设计
- 创建一个吸引人的封面页，介绍主题"大语言模型"
- 预期输出：创建slide-1-cover.html

【任务2】介绍页
- 概述大语言模型的定义和重要性
- 预期输出：创建slide-2-introduction.html

【任务3】技术原理页
- 解释大语言模型的技术基础和架构
- 预期输出：创建slide-3-technology.html`,
    expectedResult: true,
    description: '应该提取到3个任务'
  },
  {
    name: '任务执行完成回复',
    userInput: '继续执行',
    aiResponse: `任务1已完成，我已经为您创建了封面页设计。

文件已生成：slide-1-cover.html

现在开始执行任务2...`,
    expectedResult: false,
    description: '应该跳过任务规划（任务执行回复）'
  },
  {
    name: '任务执行中的回复',
    userInput: '继续',
    aiResponse: `正在执行任务2，为您创建介绍页...

基于您的需求，我将设计一个简洁明了的介绍页面。`,
    expectedResult: false,
    description: '应该跳过任务规划（任务执行中）'
  },
  {
    name: '普通对话回复',
    userInput: '这个设计怎么样？',
    aiResponse: `这个设计看起来很不错！我将为您进一步优化细节。

设计特点：
1. 现代简洁的风格
2. 清晰的视觉层次
3. 专业的配色方案`,
    expectedResult: false,
    description: '应该跳过任务规划（普通对话）'
  }
];

// 验证函数：检查任务规划逻辑
function testTaskPlanningLogic() {
  console.log('🧪 任务规划逻辑测试');
  console.log('='.repeat(50));
  
  testCases.forEach((testCase, index) => {
    console.log(`\n测试 ${index + 1}: ${testCase.name}`);
    console.log(`用户输入: "${testCase.userInput}"`);
    console.log(`AI回复: "${testCase.aiResponse.substring(0, 100)}..."`);
    console.log(`预期结果: ${testCase.expectedResult ? '提取任务' : '跳过任务规划'}`);
    console.log(`说明: ${testCase.description}`);
    
    // 检查任务标记
    const hasTaskMarkers = /【任务\d+(?:\.\d+)*】/.test(testCase.aiResponse);
    console.log(`包含任务标记: ${hasTaskMarkers}`);
    
    // 检查任务执行回复
    const isTaskExecutionReply = /任务\d+.*?已完成|任务\d+.*?执行完成|文件.*?已生成|文件.*?已创建|页面.*?已设计|正在执行任务\d+|开始执行任务\d+/.test(testCase.aiResponse);
    console.log(`任务执行回复: ${isTaskExecutionReply}`);
    
    // 模拟判断结果
    const shouldExtractTasks = hasTaskMarkers && !isTaskExecutionReply;
    console.log(`判断结果: ${shouldExtractTasks ? '提取任务' : '跳过任务规划'}`);
    console.log(`测试结果: ${shouldExtractTasks === testCase.expectedResult ? '✅ 通过' : '❌ 失败'}`);
  });
  
  return {
    status: 'completed',
    message: '任务规划逻辑测试完成'
  };
}

// 验证函数：检查首次对话
function testFirstConversation() {
  console.log('\n📝 首次对话测试');
  console.log('='.repeat(30));
  
  const firstConversationTest = testCases[0]; // 使用第一个测试用例
  
  console.log('模拟首次对话场景:');
  console.log(`输入: "${firstConversationTest.userInput}"`);
  console.log(`AI回复包含任务标记: ${/【任务\d+(?:\.\d+)*】/.test(firstConversationTest.aiResponse)}`);
  
  // 首次对话应该直接检查任务标记
  const hasTaskMarkers = /【任务\d+(?:\.\d+)*】/.test(firstConversationTest.aiResponse);
  console.log(`首次对话判断结果: ${hasTaskMarkers ? '提取任务' : '跳过任务规划'}`);
  console.log(`预期结果: 提取任务`);
  console.log(`测试结果: ${hasTaskMarkers ? '✅ 通过' : '❌ 失败'}`);
  
  return {
    status: hasTaskMarkers ? 'passed' : 'failed',
    message: hasTaskMarkers ? '首次对话测试通过' : '首次对话测试失败'
  };
}

// 验证函数：检查死循环防护
function testDeadloopPrevention() {
  console.log('\n🔄 死循环防护测试');
  console.log('='.repeat(30));
  
  const executionReplies = testCases.filter(tc => !tc.expectedResult);
  
  console.log('测试任务执行回复的防护:');
  executionReplies.forEach((testCase, index) => {
    console.log(`\n防护测试 ${index + 1}: ${testCase.name}`);
    
    const isTaskExecutionReply = /任务\d+.*?已完成|任务\d+.*?执行完成|文件.*?已生成|文件.*?已创建|页面.*?已设计|正在执行任务\d+|开始执行任务\d+/.test(testCase.aiResponse);
    const hasTaskMarkers = /【任务\d+(?:\.\d+)*】/.test(testCase.aiResponse);
    
    console.log(`任务执行回复检测: ${isTaskExecutionReply}`);
    console.log(`包含任务标记: ${hasTaskMarkers}`);
    console.log(`最终判断: ${isTaskExecutionReply || !hasTaskMarkers ? '跳过任务规划' : '提取任务'}`);
    console.log(`测试结果: ${isTaskExecutionReply || !hasTaskMarkers ? '✅ 防护生效' : '❌ 防护失效'}`);
  });
  
  return {
    status: 'completed',
    message: '死循环防护测试完成'
  };
}

// 验证函数：实际测试指南
function getTestingGuide() {
  console.log('\n📋 实际测试指南');
  console.log('='.repeat(30));
  
  const testSteps = [
    '1. 清空浏览器控制台',
    '2. 输入："写个大语言模型的PPT"',
    '3. 观察AI回复是否包含【任务1】【任务2】等标记',
    '4. 检查控制台是否显示"首次对话，直接检查任务标记: { hasTaskMarkers: true }"',
    '5. 检查是否显示"任务规划判断结果: true"',
    '6. 检查是否显示"开始提取任务..."',
    '7. 检查是否显示"提取到的任务数量: X"（X > 0）',
    '8. 检查是否创建了Todo.md文件',
    '9. 观察任务是否开始自动执行',
    '10. 重点观察：任务执行过程中不应该出现新的任务规划'
  ];
  
  testSteps.forEach(step => {
    console.log(step);
  });
  
  console.log('\n🚨 问题排查:');
  console.log('如果首次对话无法提取任务，检查:');
  console.log('- AI回复是否包含【任务X】格式的标记');
  console.log('- 控制台是否显示"检测到任务执行回复，跳过任务规划"（不应该出现）');
  console.log('- 用户输入是否包含项目关键词（如"PPT"、"写个"等）');
  
  return {
    status: 'info',
    message: '测试指南已显示'
  };
}

// 主验证函数
function verifyTaskPlanningFix() {
  console.log('🔧 任务规划修复验证');
  console.log('='.repeat(60));
  
  // 1. 逻辑测试
  const logicTest = testTaskPlanningLogic();
  console.log('\n1️⃣ 逻辑测试:', logicTest.status);
  
  // 2. 首次对话测试
  const firstTest = testFirstConversation();
  console.log('2️⃣ 首次对话测试:', firstTest.status);
  
  // 3. 死循环防护测试
  const deadloopTest = testDeadloopPrevention();
  console.log('3️⃣ 死循环防护测试:', deadloopTest.status);
  
  // 4. 实际测试指南
  const guideTest = getTestingGuide();
  console.log('4️⃣ 实际测试指南:', guideTest.status);
  
  console.log('\n🎉 修复验证总结:');
  console.log('✅ 任务执行回复检测已优化，避免误判任务规划回复');
  console.log('✅ 首次对话应该能够正常提取任务');
  console.log('✅ 死循环防护机制保持有效');
  console.log('📝 请按照测试指南进行实际验证');
  
  return {
    status: 'completed',
    message: '任务规划修复验证完成'
  };
}

// 导出验证函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    verifyTaskPlanningFix,
    testTaskPlanningLogic,
    testFirstConversation,
    testDeadloopPrevention,
    getTestingGuide
  };
}

// 如果在浏览器环境中，将函数添加到全局对象
if (typeof window !== 'undefined') {
  window.verifyTaskPlanningFix = verifyTaskPlanningFix;
  window.testTaskPlanningLogic = testTaskPlanningLogic;
  window.testFirstConversation = testFirstConversation;
  window.testDeadloopPrevention = testDeadloopPrevention;
  window.getTestingGuide = getTestingGuide;
  
  console.log('✅ 任务规划修复验证函数已加载');
  console.log('💡 使用 verifyTaskPlanningFix() 开始验证');
}

// 自动执行验证
if (typeof require === 'undefined' && typeof window !== 'undefined') {
  setTimeout(() => {
    console.log('🔧 任务规划修复验证脚本已加载');
    console.log('📞 调用 verifyTaskPlanningFix() 开始验证');
  }, 1000);
}
